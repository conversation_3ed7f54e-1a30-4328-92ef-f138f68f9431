// === BORDER AND BOUNDARY MANAGEMENT ===
// Sử dụng chiến lược chặn request tile để thay thế dữ liệu của MapTiler cho Việt Nam.

// ===== CONSTANTS AND CONFIGURATION =====
const ZOOM_THRESHOLD_COUNTRY = 5; // < 5: chỉ border quốc gia
const ZOOM_THRESHOLD_PROVINCE = 8; // >= 8: chi tiết commune

const VN_PROVINCE_TILES_URL = 'vn_province_borders/vietnam_province_borders_tiles/{z}/{x}/{y}.pbf';
const VN_COMMUNE_TILES_URL = 'vn_commune_borders/vietnam_commune_borders_tiles/{z}/{x}/{y}.pbf';

// Bounding box của Việt Nam để xác định tile cần chặn
const VIETNAM_BOUNDS = [102.144, 8.179, 109.468, 23.393]; // [minLng, minLat, maxLng, maxLat]

// ===== STATE MANAGEMENT =====
let vietnamBordersLoaded = false;
let currentZoomLevel = 0;
let currentBorderType = null; // 'province' hoặc 'commune'
let bordersInitialized = false;
let lastBorderUpdate = 0;

// ===== LAYER CONTROL MENU =====
let layerControlMenu = null;

// ===== VIETNAM TILES MANAGEMENT =====
let vietnamTilesAdded = false;

// ===================================
// ===== CORE LOGIC: TILE BLOCKING =====
// ===================================

/**
 * Chuyển đổi tọa độ tile (z, x, y) sang bounding box địa lý [minLng, minLat, maxLng, maxLat].
 * @param {number} z Zoom level
 * @param {number} x Tile X coordinate
 * @param {number} y Tile Y coordinate
 * @returns {Array<number>} Bounding box [minLng, minLat, maxLng, maxLat]
 */
function tileToBbox(z, x, y) {
    const n = Math.pow(2, z);
    const lon_deg_min = x / n * 360.0 - 180.0;
    const lat_rad_min = Math.atan(Math.sinh(Math.PI * (1 - 2 * (y + 1) / n)));
    const lat_deg_min = lat_rad_min * 180.0 / Math.PI;

    const lon_deg_max = (x + 1) / n * 360.0 - 180.0;
    const lat_rad_max = Math.atan(Math.sinh(Math.PI * (1 - 2 * y / n)));
    const lat_deg_max = lat_rad_max * 180.0 / Math.PI;

    return [lon_deg_min, lat_deg_min, lon_deg_max, lat_deg_max];
}

/**
 * Kiểm tra xem một tile (dựa trên z, x, y) có giao với bounding box của Việt Nam không.
 * @param {number} z
 * @param {number} x
 * @param {number} y
 * @returns {boolean}
 */
function isTileInVietnam(z, x, y) {
    const [tileLngMin, tileLatMin, tileLngMax, tileLatMax] = tileToBbox(z, x, y);
    const [vnLngMin, vnLatMin, vnLngMax, vnLatMax] = VIETNAM_BOUNDS;

    // Logic kiểm tra giao nhau (intersection) của 2 hình chữ nhật
    const intersects = (tileLngMin < vnLngMax && tileLngMax > vnLngMin) &&
                       (tileLatMin < vnLatMax && tileLatMax > vnLatMin);
    return intersects;
}

/**
 * Hàm này sẽ được Mapbox gọi trước mỗi yêu cầu mạng.
 * Nó sẽ chặn các tile của MapTiler trong khu vực Việt Nam.
 * QUAN TRỌNG: Hàm này phải được truyền vào khi khởi tạo Map.
 */
function customTransformRequest(url, resourceType) {
    // Chỉ can thiệp vào source vector 'countries' của MapTiler, nơi chứa các layer ranh giới.
    if (resourceType === 'Tile' && url.includes('api.maptiler.com/tiles/countries')) {
        const match = url.match(/\/(\d+)\/(\d+)\/(\d+)\.pbf/);
        if (match) {
            const z = parseInt(match[1], 10);
            const x = parseInt(match[2], 10);
            const y = parseInt(match[3], 10);

            // Nếu tile này nằm trong khu vực VN
            if (isTileInVietnam(z, x, y)) {
                console.log(`🚫 BLOCKING Vietnam tile: z=${z}, x=${x}, y=${y}`);
                // Trả về một URL rỗng để hủy yêu cầu. Map sẽ coi như không có dữ liệu ở tile này.
                return { url: '' };
            }
        }
    }
    // Đối với tất cả các yêu cầu khác, để chúng tiếp tục bình thường
    return { url: url };
}


// ======================================
// ===== LAYER MANAGEMENT & DISPLAY =====
// ======================================

/**
 * Thêm các layer ranh giới tùy chỉnh từ MapTiler
 */
function addCustomBorderLayers() {
    if (!map.isStyleLoaded()) return;

    try {
        // Thêm source cho ranh giới từ MapTiler Countries
        if (!map.getSource('maptiler-countries')) {
            map.addSource('maptiler-countries', {
                type: 'vector',
                url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${maptilersdk.config.apiKey}`
            });
            console.log('Added MapTiler Countries source');
        }

        // 1. Thêm ranh giới quốc gia (admin_level=2)
        if (!map.getLayer('country-boundaries')) {
            map.addLayer({
                id: 'country-boundaries',
                type: 'line',
                source: 'maptiler-countries',
                'source-layer': 'boundary',
                filter: ['==', 'admin_level', 2],
                paint: {
                    'line-color': '#333333',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        2, 0.5,
                        6, 1.0,
                        10, 1.5
                    ],
                    'line-opacity': 0.8
                }
            });
            console.log('Added country boundaries layer');
        }

        // 2. Thêm ranh giới cấp 1 (admin_level=4 - states/provinces)
        if (!map.getLayer('state-boundaries')) {
            map.addLayer({
                id: 'state-boundaries',
                type: 'line',
                source: 'maptiler-countries',
                'source-layer': 'boundary',
                filter: ['==', 'admin_level', 4],
                paint: {
                    'line-color': '#666666',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        4, 0.3,
                        8, 0.8,
                        12, 1.2
                    ],
                    'line-opacity': 0.6,
                    'line-dasharray': [2, 2]
                }
            });
            console.log('Added state boundaries layer');
        }

        // 3. Đảm bảo các border layer luôn ở trên cùng
        setTimeout(() => {
            if (map.getLayer('state-boundaries')) {
                map.moveLayer('state-boundaries');
            }
            if (map.getLayer('country-boundaries')) {
                map.moveLayer('country-boundaries');
            }
        }, 100);

    } catch (error) {
        console.error('Error adding custom border layers:', error);
    }
}

/**
 * Thêm tất cả các layer ranh giới (cả của MapTiler và của Việt Nam) một lần duy nhất.
 * Các layer của Việt Nam sẽ được ẩn đi ban đầu.
 */
function addAllBorderLayers() {
    if (!map.isStyleLoaded()) return;

    // 1. Thêm source và các layer ranh giới của MapTiler (nếu chưa có)
    // Source này sẽ bị `transformRequest` chặn có chọn lọc.
    if (!map.getSource('maptiler-countries')) {
        map.addSource('maptiler-countries', {
            type: 'vector',
            url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${maptilersdk.config.apiKey}`
        });
    }

    // Các layer ranh giới tỉnh/xã của MapTiler.
    // Chúng sẽ tự động "biến mất" trên VN nhờ `transformRequest`.
    const maptilerLayers = [
        {
            id: 'state-boundaries', // Tỉnh/thành
            filter: ['==', 'admin_level', 4],
            paint: { 'line-color': '#666', 'line-width': 0.8, 'line-dasharray': [3, 3] }
        },
        {
            id: 'commune-boundaries', // Huyện/xã
            filter: ['==', 'admin_level', 6],
            paint: { 'line-color': '#888', 'line-width': 0.5, 'line-dasharray': [2, 2] }
        }
    ];

    maptilerLayers.forEach(layerInfo => {
        if (!map.getLayer(layerInfo.id)) {
            map.addLayer({
                id: layerInfo.id,
                type: 'line',
                source: 'maptiler-countries',
                'source-layer': 'boundary',
                filter: ['all', layerInfo.filter],
                paint: layerInfo.paint
            });
        }
    });

    // 2. Thêm Vietnam tiles (province và commune) - FORCE ADD
    console.log('🇻🇳 Adding Vietnam tiles...');
    addVietnamProvinceTiles();
    addVietnamCommuneTiles();

    vietnamBordersLoaded = true;

    // Đợi một chút rồi mới ensure borders on top
    setTimeout(() => {
        ensureBoundariesOnTop();
    }, 100);
}

// ===== VIETNAM TILES BORDER FUNCTIONS =====

/**
 * Add Vietnam province tiles (sử dụng tiles thay vì GeoJSON)
 */
function addVietnamProvinceTiles() {
    if (!map.isStyleLoaded()) {
        console.log('❌ Map style not loaded, cannot add VN province tiles');
        return;
    }

    try {
        // Thêm source cho Vietnam province tiles
        if (!map.getSource('vn-provinces')) {
            console.log('🇻🇳 Adding Vietnam province tiles source...');
            map.addSource('vn-provinces', {
                type: 'vector',
                tiles: [VN_PROVINCE_TILES_URL],
                minzoom: 5,
                maxzoom: 12
            });
            console.log('✅ Added Vietnam province tiles source:', VN_PROVINCE_TILES_URL);
        } else {
            console.log('ℹ️ Vietnam province source already exists');
        }

        // Thêm layer cho Vietnam province borders
        if (!map.getLayer('vn-province-borders')) {
            console.log('🇻🇳 Adding Vietnam province borders layer...');
            map.addLayer({
                id: 'vn-province-borders',
                type: 'line',
                source: 'vn-provinces',
                'source-layer': 'default', // Thử 'default' trước, có thể cần đổi
                paint: {
                    'line-color': '#d35400',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        5, 1.0,
                        8, 1.5,
                        12, 2.0
                    ],
                    'line-opacity': 0.9
                },
                layout: { 'visibility': 'visible' } // Hiện ngay để test
            });
            console.log('✅ Added Vietnam province borders layer');
        } else {
            console.log('ℹ️ Vietnam province layer already exists');
        }

        vietnamTilesAdded = true;

    } catch (error) {
        console.error('❌ Error adding Vietnam province tiles:', error);
    }
}

/**
 * Add Vietnam commune tiles
 */
function addVietnamCommuneTiles() {
    if (!map.isStyleLoaded()) {
        console.log('❌ Map style not loaded, cannot add VN commune tiles');
        return;
    }

    try {
        // Thêm source cho Vietnam commune tiles
        if (!map.getSource('vn-communes')) {
            console.log('🇻🇳 Adding Vietnam commune tiles source...');
            map.addSource('vn-communes', {
                type: 'vector',
                tiles: [VN_COMMUNE_TILES_URL],
                minzoom: 8,
                maxzoom: 14
            });
            console.log('✅ Added Vietnam commune tiles source:', VN_COMMUNE_TILES_URL);
        } else {
            console.log('ℹ️ Vietnam commune source already exists');
        }

        // Thêm layer cho Vietnam commune borders
        if (!map.getLayer('vn-commune-borders')) {
            console.log('🇻🇳 Adding Vietnam commune borders layer...');
            map.addLayer({
                id: 'vn-commune-borders',
                type: 'line',
                source: 'vn-communes',
                'source-layer': 'default', // Thử 'default' trước, có thể cần đổi
                paint: {
                    'line-color': '#8e44ad',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        8, 0.8,
                        10, 1.0,
                        14, 1.5
                    ],
                    'line-opacity': 0.8
                },
                layout: { 'visibility': 'none' } // Ban đầu ẩn
            });
            console.log('✅ Added Vietnam commune borders layer');
        } else {
            console.log('ℹ️ Vietnam commune layer already exists');
        }

    } catch (error) {
        console.error('❌ Error adding Vietnam commune tiles:', error);
    }
}

/**
 * Hide Vietnam borders (both province and commune)
 */
function hideVietnamBorders() {
    try {
        if (map.getLayer('vn-province-borders')) {
            map.setLayoutProperty('vn-province-borders', 'visibility', 'none');
        }
        if (map.getLayer('vn-commune-borders')) {
            map.setLayoutProperty('vn-commune-borders', 'visibility', 'none');
        }
    } catch (e) {
        console.log('Could not hide Vietnam borders');
    }
}

/**
 * Show Vietnam province borders
 */
function showVietnamProvinceBorders() {
    try {
        if (map.getLayer('vn-province-borders')) {
            map.setLayoutProperty('vn-province-borders', 'visibility', 'visible');
            console.log('✅ Showed Vietnam province borders');
        } else {
            console.log('❌ Vietnam province borders layer not found');
        }
    } catch (e) {
        console.error('❌ Could not show Vietnam province borders:', e);
    }
}

/**
 * Hide Vietnam province borders
 */
function hideVietnamProvinceBorders() {
    try {
        if (map.getLayer('vn-province-borders')) {
            map.setLayoutProperty('vn-province-borders', 'visibility', 'none');
            console.log('✅ Hid Vietnam province borders');
        } else {
            console.log('❌ Vietnam province borders layer not found');
        }
    } catch (e) {
        console.error('❌ Could not hide Vietnam province borders:', e);
    }
}

/**
 * Show Vietnam commune borders
 */
function showVietnamCommuneBorders() {
    try {
        if (map.getLayer('vn-commune-borders')) {
            map.setLayoutProperty('vn-commune-borders', 'visibility', 'visible');
        }
    } catch (e) {
        console.log('Could not show Vietnam commune borders');
    }
}

/**
 * Hide Vietnam commune borders
 */
function hideVietnamCommuneBorders() {
    try {
        if (map.getLayer('vn-commune-borders')) {
            map.setLayoutProperty('vn-commune-borders', 'visibility', 'none');
        }
    } catch (e) {
        console.log('Could not hide Vietnam commune borders');
    }
}

/**
 * Logic chính: Bật/tắt các layer của Việt Nam dựa trên mức zoom.
 * Logic này đơn giản vì `transformRequest` đã làm phần việc khó.
 */
function handleViewChange() {
    if (!map.isStyleLoaded() || !vietnamBordersLoaded) return;

    const zoom = map.getZoom();
    currentZoomLevel = zoom;
    let vnBorderVisible = false;

    // --- Quản lý Ranh giới Tỉnh VN ---
    if (map.getLayer('vn-province-borders')) {
        const visibility = (zoom >= ZOOM_THRESHOLD_COUNTRY && zoom < ZOOM_THRESHOLD_PROVINCE) ? 'visible' : 'none';
        map.setLayoutProperty('vn-province-borders', 'visibility', visibility);
        if (visibility === 'visible') {
            vnBorderVisible = true;
            currentBorderType = 'province';
        }
    }

    // --- Quản lý Ranh giới Xã VN ---
    if (map.getLayer('vn-commune-borders')) {
        const visibility = (zoom >= ZOOM_THRESHOLD_PROVINCE) ? 'visible' : 'none';
        map.setLayoutProperty('vn-commune-borders', 'visibility', visibility);
        if (visibility === 'visible') {
            vnBorderVisible = true;
            currentBorderType = 'commune';
        }
    }

    if (!vnBorderVisible) {
        currentBorderType = null;
    }

    // Đảm bảo layer custom luôn ở trên cùng khi nó hiển thị
    ensureBoundariesOnTop();
}

/**
 * Đảm bảo các layer ranh giới và nhãn luôn ở trên cùng.
 */
function ensureBoundariesOnTop() {
    if (!map.isStyleLoaded()) return;
    
    const layersToMove = [
        'state-boundaries', 'commune-boundaries', 'Country border', // Maptiler borders
        'vn-province-borders', 'vn-commune-borders', // Custom VN borders
        'City labels', 'Country labels' // Labels
    ];

    layersToMove.forEach(layerId => {
        try {
            if (map.getLayer(layerId)) {
                map.moveLayer(layerId);
            }
        } catch (e) { /* silent */ }
    });
}


/**
 * Hide MapTiler state borders (admin_level=4)
 */
function hideMapTilerStateBorders() {
    try {
        if (map.getLayer('state-boundaries')) {
            map.setLayoutProperty('state-boundaries', 'visibility', 'none');
        }
    } catch (e) {
        console.log('Could not hide MapTiler state borders');
    }
}

/**
 * Show MapTiler state borders (admin_level=4)
 */
function showMapTilerStateBorders() {
    try {
        if (map.getLayer('state-boundaries')) {
            map.setLayoutProperty('state-boundaries', 'visibility', 'visible');
        }
    } catch (e) {
        console.log('Could not show MapTiler state borders');
    }
}

/**
 * Force refresh Vietnam borders
 */
function forceReloadVietnamProvinceBorders() {
    // Hide current borders
    hideVietnamBorders();

    // Re-add tiles
    addVietnamProvinceTiles();
    addVietnamCommuneTiles();

    // Apply current zoom logic
    handleZoomChange();

    console.log('🔄 Force reloaded Vietnam borders');
}

/**
 * Handle zoom level changes - sử dụng tiles logic
 */
function handleZoomChange() {
    if (!map.isStyleLoaded()) return;

    const newZoomLevel = map.getZoom();
    const zoomChanged = Math.abs(newZoomLevel - currentZoomLevel) > 0.1;

    if (!zoomChanged) return;

    currentZoomLevel = newZoomLevel;

    if (newZoomLevel >= ZOOM_THRESHOLD_PROVINCE) {
        // Zoom >= 8: Hiển thị commune borders
        hideVietnamProvinceBorders();
        showVietnamCommuneBorders();
        showMapTilerStateBorders();
        currentBorderType = 'commune';

        console.log(`🔍 Zoom ${newZoomLevel.toFixed(1)}: Showing commune borders`);

    } else if (newZoomLevel >= ZOOM_THRESHOLD_COUNTRY) {
        // Zoom 5-8: Hiển thị province borders
        hideVietnamCommuneBorders();
        showVietnamProvinceBorders();
        showMapTilerStateBorders();
        currentBorderType = 'province';

        console.log(`🔍 Zoom ${newZoomLevel.toFixed(1)}: Showing province borders`);

    } else {
        // Zoom < 5: Chỉ hiển thị country borders
        hideVietnamBorders();
        hideMapTilerStateBorders();
        currentBorderType = null;

        console.log(`🔍 Zoom ${newZoomLevel.toFixed(1)}: Showing only country borders`);
    }
}

/**
 * Handle viewport changes (pan/move) - đơn giản hơn với tiles
 */
function handleViewportChange() {
    if (!map.isStyleLoaded()) return;

    // Với tiles, không cần reload gì cả vì tiles tự động load theo viewport
    // Chỉ cần đảm bảo zoom logic được áp dụng
    handleZoomChange();

    console.log('🗺️ Viewport changed - tiles auto-loaded');
}

// Debounce cho view change
let viewChangeTimeout = null;
function debouncedHandleViewChange() {
    clearTimeout(viewChangeTimeout);
    viewChangeTimeout = setTimeout(handleViewChange, 200);
}

// Debounce cho zoom change
let zoomChangeTimeout = null;
function debouncedHandleZoomChange() {
    clearTimeout(zoomChangeTimeout);
    zoomChangeTimeout = setTimeout(handleZoomChange, 500);
}

// Debounce cho viewport change
let viewportChangeTimeout = null;
function debouncedHandleViewportChange() {
    clearTimeout(viewportChangeTimeout);
    viewportChangeTimeout = setTimeout(handleViewportChange, 300);
}

// =======================
// ===== INITIALIZATION & UI =====
// =======================

/**
 * Khởi tạo toàn bộ hệ thống quản lý ranh giới.
 */
function initializeBorderManager() {
    if (bordersInitialized) return;

    console.log('🗺️ Initializing border manager...');

    // Setup border management on style load - CHỈ CHẠY MỘT LẦN
    let styleDataHandled = false;
    map.on('styledata', () => {
        if (map.isStyleLoaded() && !styleDataHandled) {
            styleDataHandled = true;
            console.log('🎨 Style loaded - adding border layers...');
            addAllBorderLayers();
            handleViewChange(); // Chạy lần đầu để set trạng thái đúng

            // Chỉ tạo menu một lần
            if (!layerControlMenu) {
                createLayerControlMenu();
            }
        }
    });

    // Chỉ ensure borders một lần khi map load
    map.on('load', function() {
        if (!bordersInitialized) {
            setTimeout(() => {
                console.log('🗺️ Map loaded - ensuring borders on top...');
                ensureBoundariesOnTop();

                // Force add VN tiles ngay
                addVietnamProvinceTiles();
                addVietnamCommuneTiles();

                bordersInitialized = true;
                console.log('🗺️ Borders initialized on map load');

                // Khởi tạo zoom level và xử lý border theo zoom
                currentZoomLevel = map.getZoom();
                debouncedHandleZoomChange();
            }, 500);
        }
    });

    // Event listener cho zoom change
    map.on('zoom', debouncedHandleZoomChange);
    map.on('zoomend', debouncedHandleZoomChange);

    // Event listener cho viewport change (pan/move)
    map.on('moveend', debouncedHandleViewportChange);
    map.on('dragend', debouncedHandleViewportChange);

    // Legacy support for old handleViewChange
    map.on('zoom', debouncedHandleViewChange);
    map.on('move', debouncedHandleViewChange);

    console.log('🗺️ Border manager initialized with Vietnam tiles support');
}

// Reset state
function resetBorderState() {
    bordersInitialized = false;
    vietnamBordersLoaded = false;
    currentBorderType = null;
    vietnamTilesAdded = false;
}

// Hàm thêm layer border riêng
function addBorderLayer() {
    // Kiểm tra và thêm các layer border có sẵn của MapTiler
    const borderLayers = ['boundary', 'admin', 'country-border'];

    borderLayers.forEach(layerName => {
        try {
            if (map.getLayer(layerName)) {
                // Di chuyển layer border lên trên temp layer
                map.moveLayer(layerName, 'temperature-layer');
                console.log(`Moved border layer ${layerName} above temp layer`);
            }
        } catch (e) {
            console.log(`Could not move border layer ${layerName}:`, e.message);
        }
    });
}

// Hàm để đảm bảo borders luôn ở trên - với kiểm tra để tránh spam
function ensureAllBordersOnTop() {
    if (!map.isStyleLoaded()) return;

    setTimeout(() => {
        ensureBoundariesOnTop();
        addCustomBorderLayers();
        console.log('✅ All borders moved to top');
    }, 100);
}

// ===== UI CONTROL MENU =====
function createLayerControlMenu() {
    console.log("Creating Layer Control Menu...");
    if (layerControlMenu) document.body.removeChild(layerControlMenu);

    layerControlMenu = document.createElement('div');
    layerControlMenu.id = 'layer-control-menu';
    layerControlMenu.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #ccc;
        border-radius: 8px;
        padding: 15px;
        font-family: Arial, sans-serif;
        font-size: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        min-width: 200px;
        max-height: 400px;
        overflow-y: auto;
    `;

    const header = document.createElement('div');
    header.innerHTML = '<strong>🗺️ Layer Controls</strong>';
    header.style.cssText = `
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;
        color: #333;
    `;
    layerControlMenu.appendChild(header);

    // Border Controls Section
    const borderSection = document.createElement('div');
    borderSection.innerHTML = '<strong>🔲 Border Controls:</strong>';
    borderSection.style.marginBottom = '8px';
    layerControlMenu.appendChild(borderSection);

    // Country Borders Toggle
    const countryToggle = createToggleButton('Country Borders', 'country-boundaries', true);
    layerControlMenu.appendChild(countryToggle);

    // State Borders Toggle
    const stateToggle = createToggleButton('State/Province Borders', 'state-boundaries', true);
    layerControlMenu.appendChild(stateToggle);

    // Vietnam Province Borders Toggle
    const vnProvinceToggle = createToggleButton('Vietnam Province Borders', 'vn-province-borders', false);
    layerControlMenu.appendChild(vnProvinceToggle);

    // Vietnam Commune Borders Toggle
    const vnCommuneToggle = createToggleButton('Vietnam Commune Borders', 'vn-commune-borders', false);
    layerControlMenu.appendChild(vnCommuneToggle);

    // Utility Buttons Section
    const utilitySection = document.createElement('div');
    utilitySection.innerHTML = '<strong>🔧 Utilities:</strong>';
    utilitySection.style.cssText = 'margin-top: 15px; margin-bottom: 8px;';
    layerControlMenu.appendChild(utilitySection);

    // Refresh Vietnam Borders Button
    const refreshBtn = document.createElement('button');
    refreshBtn.innerHTML = '🔄 Refresh VN Borders';
    refreshBtn.style.cssText = `
        width: 100%;
        padding: 5px;
        margin: 2px 0;
        border: 1px solid #007cba;
        background: #007cba;
        color: white;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
    `;
    refreshBtn.onclick = forceReloadVietnamProvinceBorders;
    layerControlMenu.appendChild(refreshBtn);

    // Test VN Tiles Button
    const testBtn = document.createElement('button');
    testBtn.innerHTML = '🧪 Test VN Tiles';
    testBtn.style.cssText = `
        width: 100%;
        padding: 5px;
        margin: 2px 0;
        border: 1px solid #e74c3c;
        background: #e74c3c;
        color: white;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
    `;
    testBtn.onclick = function() {
        console.log('🧪 Testing VN tiles...');
        console.log('VN Province source:', map.getSource('vn-provinces'));
        console.log('VN Province layer:', map.getLayer('vn-province-borders'));
        console.log('VN Commune source:', map.getSource('vn-communes'));
        console.log('VN Commune layer:', map.getLayer('vn-commune-borders'));

        // Force show province borders
        if (map.getLayer('vn-province-borders')) {
            map.setLayoutProperty('vn-province-borders', 'visibility', 'visible');
            console.log('✅ Forced VN province borders visible');
        } else {
            console.log('❌ VN province borders layer not found');
        }
    };
    layerControlMenu.appendChild(testBtn);

    // Status Panel
    const statusSection = document.createElement('div');
    statusSection.id = 'status-panel';
    statusSection.style.cssText = `
        margin-top: 15px;
        padding: 8px;
        background: #f5f5f5;
        border-radius: 4px;
        font-size: 11px;
        line-height: 1.4;
    `;
    layerControlMenu.appendChild(statusSection);

    document.body.appendChild(layerControlMenu);

    // Update status every second
    setInterval(() => {
        const zoom = map.getZoom().toFixed(1);
        const border = currentBorderType || 'none';
        const vnTilesStatus = vietnamTilesAdded ? 'loaded' : 'not loaded';
        statusSection.innerHTML = `
            <strong>📊 Status:</strong><br>
            Zoom: ${zoom}<br>
            Current VN Border: ${border}<br>
            VN Tiles: ${vnTilesStatus}
        `;
    }, 1000);
}

function createToggleButton(label, layerId, defaultVisible) {
    const container = document.createElement('div');
    container.style.cssText = 'margin: 4px 0; display: flex; align-items: center;';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.checked = defaultVisible;
    checkbox.style.marginRight = '8px';

    const labelElement = document.createElement('label');
    labelElement.textContent = label;
    labelElement.style.cssText = 'font-size: 11px; cursor: pointer; flex: 1;';

    checkbox.onchange = function() {
        const visibility = this.checked ? 'visible' : 'none';
        console.log(`🔘 Toggle ${label} (${layerId}): ${visibility}`);

        try {
            if (layerId === 'vn-province-borders') {
                // Handle Vietnam province borders
                if (this.checked) {
                    showVietnamProvinceBorders();
                } else {
                    hideVietnamProvinceBorders();
                }
            } else if (layerId === 'vn-commune-borders') {
                // Handle Vietnam commune borders
                if (this.checked) {
                    showVietnamCommuneBorders();
                } else {
                    hideVietnamCommuneBorders();
                }
            } else if (map.getLayer(layerId)) {
                map.setLayoutProperty(layerId, 'visibility', visibility);
                console.log(`✅ Set ${layerId} visibility to ${visibility}`);
            } else {
                console.log(`❌ Layer ${layerId} not found`);
            }
        } catch (e) {
            console.error(`❌ Could not toggle ${layerId}:`, e);
        }
    };

    labelElement.onclick = () => checkbox.click();

    container.appendChild(checkbox);
    container.appendChild(labelElement);

    return container;
}


// ======================
// ===== EXPORT =====
// ======================

window.borderManager = {
    // Hàm chính để khởi tạo
    initializeBorderManager,

    // Hàm quan trọng cần truyền vào Map constructor
    customTransformRequest,

    // Các hàm tiện ích khác nếu cần
    resetBorderState,
    ensureBoundariesOnTop,
    ensureAllBordersOnTop,
    addCustomBorderLayers,
    addBorderLayer,

    // Vietnam tiles border functions
    addVietnamProvinceTiles,
    addVietnamCommuneTiles,
    hideVietnamBorders,
    hideVietnamProvinceBorders,
    showVietnamProvinceBorders,
    hideVietnamCommuneBorders,
    showVietnamCommuneBorders,
    hideMapTilerStateBorders,
    showMapTilerStateBorders,
    handleZoomChange,
    handleViewportChange,
    forceReloadVietnamProvinceBorders,

    // UI functions
    createLayerControlMenu,

    // Getters
    getCurrentBorderType: () => currentBorderType,
    getVietnamBordersLoaded: () => vietnamBordersLoaded,
    getCurrentZoomLevel: () => currentZoomLevel,
    getZoomThresholdCountry: () => ZOOM_THRESHOLD_COUNTRY,
    getZoomThresholdProvince: () => ZOOM_THRESHOLD_PROVINCE,
    getVietnamTilesAdded: () => vietnamTilesAdded
};
