<!DOCTYPE html>
<html>
<head>
    <title>Console Test</title>
</head>
<body>
    <h1>Console Test</h1>
    <p>Mở Developer Tools (F12) và xem Console tab</p>
    
    <script>
        console.log('=== CONSOLE TEST ===');
        console.log('Server status check...');
        
        // Test kết nối đến tileserver
        fetch('http://localhost:6000')
            .then(response => {
                console.log('✅ Server response:', response.status, response.statusText);
                return response.text();
            })
            .then(data => {
                console.log('✅ Server data length:', data.length);
            })
            .catch(error => {
                console.error('❌ Server error:', error);
            });
        
        // Test data endpoint
        fetch('http://localhost:6000/data/vn_communes.json')
            .then(response => {
                console.log('✅ Data endpoint response:', response.status, response.statusText);
                return response.json();
            })
            .then(data => {
                console.log('✅ Data endpoint data:', data);
            })
            .catch(error => {
                console.error('❌ Data endpoint error:', error);
            });
        
        console.log('=== END TEST ===');
    </script>
</body>
</html> 