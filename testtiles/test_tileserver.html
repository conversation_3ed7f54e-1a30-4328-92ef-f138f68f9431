<!DOCTYPE html>
<html>
<head>
    <title>Tileserver Test</title>
    <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
    <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />
    <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
    </style>
</head>
<body>
    <div id='map'></div>
    <script>
        mapboxgl.accessToken = 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';
        
        const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [105.8342, 21.0278], // Hanoi
            zoom: 6
        });

        map.on('load', () => {
            // Thử thêm source từ tileserver
            map.addSource('vn-communes', {
                type: 'vector',
                url: 'http://localhost:8080/data/vn_communes.json'
            });

            map.addLayer({
                'id': 'vn-communes-fill',
                'type': 'fill',
                'source': 'vn-communes',
                'source-layer': 'communes',
                'paint': {
                    'fill-color': '#088',
                    'fill-opacity': 0.6
                }
            });

            map.addLayer({
                'id': 'vn-communes-outline',
                'type': 'line',
                'source': 'vn-communes',
                'source-layer': 'communes',
                'paint': {
                    'line-color': '#000',
                    'line-width': 1
                }
            });
        });
    </script>
</body>
</html> 